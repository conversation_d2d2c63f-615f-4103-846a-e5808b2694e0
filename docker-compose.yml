version: '3.4'

services:
  web-api:
    image: ${DOCKER_REGISTRY-}webapi
    container_name: web-api
    build:
      context: .
      dockerfile: src/Web.Api/Dockerfile
    ports:
      - 5000:8080
      - 5001:8081

  postgres:
    image: postgres:latest
    container_name: postgres
    environment:
      - POSTGRES_DB=clean-architecture
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - ./.containers/db:/var/lib/postgresql/data
    ports:
      - 5432:5432

  seq:
    image: datalust/seq:latest
    container_name: seq
    environment:
      - ACCEPT_EULA=Y
    ports:
      - 8081:80
  
  redis:
    image: redis:latest
    container_name: redis
    ports:
      - 6379:6379
